const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Load environment variables manually
const envPath = path.join(__dirname, "..", ".env");
const envContent = fs.readFileSync(envPath, "utf8");
const envVars = {};

envContent.split("\n").forEach((line) => {
  const [key, value] = line.split("=");
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});

const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey =
  envVars.SUPABASE_SERVICE_ROLE_KEY || envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing Supabase environment variables");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupGalleryTable() {
  console.log("Setting up gallery table...");

  try {
    // Create gallery table
    const { error: createTableError } = await supabase.rpc("exec_sql", {
      sql: `
        -- Create gallery table
        CREATE TABLE IF NOT EXISTS gallery (
          id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
          title text NOT NULL,
          description text NOT NULL,
          image_url text NOT NULL,
          activity_type text NOT NULL DEFAULT 'Kegiatan Umum',
          created_at timestamptz DEFAULT now(),
          updated_at timestamptz DEFAULT now()
        );

        -- Enable RLS
        ALTER TABLE gallery ENABLE ROW LEVEL SECURITY;

        -- Create policies for public read access
        CREATE POLICY "Allow public read access on gallery"
          ON gallery FOR SELECT
          TO anon, authenticated
          USING (true);

        -- Create policies for authenticated users to manage gallery
        CREATE POLICY "Allow authenticated users to manage gallery"
          ON gallery FOR ALL
          TO authenticated
          USING (true);
      `,
    });

    if (createTableError) {
      console.error("Error creating table:", createTableError);
      return;
    }

    // Insert sample data
    const { error: insertError } = await supabase.from("gallery").insert([
      {
        title: "Gotong Royong Membersihkan Jalan",
        description:
          "Kegiatan gotong royong warga dalam membersihkan jalan utama desa",
        image_url:
          "https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800",
        activity_type: "Gotong Royong",
      },
      {
        title: "Pelatihan UMKM",
        description:
          "Pelatihan pengembangan usaha mikro kecil menengah untuk warga",
        image_url:
          "https://images.unsplash.com/photo-1552664730-d307ca884978?w=800",
        activity_type: "Pelatihan",
      },
      {
        title: "Festival Budaya Desa",
        description:
          "Perayaan festival budaya tahunan dengan berbagai pertunjukan tradisional",
        image_url:
          "https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=800",
        activity_type: "Budaya",
      },
    ]);

    if (insertError) {
      console.error("Error inserting sample data:", insertError);
      return;
    }

    console.log("✅ Gallery table setup completed successfully!");
    console.log("✅ Sample data inserted!");
  } catch (error) {
    console.error("Error setting up gallery:", error);
  }
}

// Alternative method using direct SQL execution
async function setupGalleryTableDirect() {
  console.log("Setting up gallery table using direct method...");

  try {
    // Check if table exists first
    const { data: tables } = await supabase
      .from("information_schema.tables")
      .select("table_name")
      .eq("table_schema", "public")
      .eq("table_name", "gallery");

    if (tables && tables.length > 0) {
      console.log("Gallery table already exists!");
      return;
    }

    // Create table using INSERT (this is a workaround)
    const { error } = await supabase.from("gallery").select("*").limit(1);

    if (
      error &&
      error.message.includes('relation "public.gallery" does not exist')
    ) {
      console.log(
        "Gallery table does not exist. Please run the migration manually in Supabase dashboard."
      );
      console.log(
        "Copy and paste the SQL from supabase/migrations/20250806151011_azure_glitter.sql"
      );
      console.log("Look for the gallery table creation section.");
    } else {
      console.log("Gallery table exists or other error:", error);
    }
  } catch (error) {
    console.error("Error checking gallery table:", error);
  }
}

// Run the setup
if (require.main === module) {
  setupGalleryTableDirect();
}

module.exports = { setupGalleryTable, setupGalleryTableDirect };
