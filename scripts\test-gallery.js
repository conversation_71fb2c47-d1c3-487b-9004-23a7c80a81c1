const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Load environment variables manually
const envPath = path.join(__dirname, "..", ".env");
const envContent = fs.readFileSync(envPath, "utf8");
const envVars = {};

envContent.split("\n").forEach((line) => {
  const [key, value] = line.split("=");
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});

const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey =
  envVars.SUPABASE_SERVICE_ROLE_KEY || envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing Supabase environment variables");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testGalleryFunctionality() {
  console.log("Testing gallery functionality...");
  
  try {
    // Test 1: Try to read from gallery table
    console.log("1. Testing read access to gallery table...");
    const { data: existingData, error: readError } = await supabase
      .from('gallery')
      .select('*')
      .limit(5);

    if (readError) {
      console.error('❌ Error reading from gallery table:', readError.message);
      
      if (readError.message.includes('relation "public.gallery" does not exist')) {
        console.log('📝 Gallery table does not exist. Creating it now...');
        await createGalleryTable();
        return;
      }
    } else {
      console.log('✅ Successfully read from gallery table');
      console.log(`   Found ${existingData.length} existing items`);
      
      if (existingData.length > 0) {
        console.log('   Sample item:', existingData[0].title);
      }
    }

    // Test 2: Try to insert a test item
    console.log("2. Testing insert functionality...");
    const testItem = {
      title: 'Test Gallery Item',
      description: 'This is a test item for gallery functionality',
      image_url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
      activity_type: 'Test'
    };

    const { data: insertData, error: insertError } = await supabase
      .from('gallery')
      .insert([testItem])
      .select();

    if (insertError) {
      console.error('❌ Error inserting test item:', insertError.message);
    } else {
      console.log('✅ Successfully inserted test item');
      
      // Clean up - delete the test item
      const { error: deleteError } = await supabase
        .from('gallery')
        .delete()
        .eq('id', insertData[0].id);
        
      if (!deleteError) {
        console.log('✅ Successfully cleaned up test item');
      }
    }

    // Test 3: Test different activity types
    console.log("3. Testing activity type filtering...");
    const { data: filteredData, error: filterError } = await supabase
      .from('gallery')
      .select('*')
      .eq('activity_type', 'Gotong Royong');

    if (filterError) {
      console.error('❌ Error filtering by activity type:', filterError.message);
    } else {
      console.log('✅ Successfully filtered by activity type');
      console.log(`   Found ${filteredData.length} items with type 'Gotong Royong'`);
    }

    console.log('\n🎉 Gallery functionality test completed!');
    
  } catch (error) {
    console.error('❌ Unexpected error during testing:', error);
  }
}

async function createGalleryTable() {
  console.log('Creating gallery table and inserting sample data...');
  
  try {
    // Insert sample data (this will create the table if it doesn't exist due to our migration)
    const sampleData = [
      {
        title: 'Gotong Royong Membersihkan Jalan',
        description: 'Kegiatan gotong royong warga dalam membersihkan jalan utama desa',
        image_url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800',
        activity_type: 'Gotong Royong'
      },
      {
        title: 'Pelatihan UMKM',
        description: 'Pelatihan pengembangan usaha mikro kecil menengah untuk warga',
        image_url: 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=800',
        activity_type: 'Pelatihan'
      },
      {
        title: 'Festival Budaya Desa',
        description: 'Perayaan festival budaya tahunan dengan berbagai pertunjukan tradisional',
        image_url: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=800',
        activity_type: 'Budaya'
      }
    ];

    const { data, error } = await supabase
      .from('gallery')
      .insert(sampleData)
      .select();

    if (error) {
      console.error('❌ Error creating sample data:', error.message);
      console.log('\n📋 Manual Setup Required:');
      console.log('Please run the following SQL in your Supabase dashboard:');
      console.log('\n' + getSQLScript());
    } else {
      console.log('✅ Successfully created gallery table and inserted sample data!');
      console.log(`   Inserted ${data.length} sample items`);
    }
    
  } catch (error) {
    console.error('❌ Error during table creation:', error);
  }
}

function getSQLScript() {
  return `
-- Create gallery table
CREATE TABLE IF NOT EXISTS gallery (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  description text NOT NULL,
  image_url text NOT NULL,
  activity_type text NOT NULL DEFAULT 'Kegiatan Umum',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE gallery ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access
CREATE POLICY "Allow public read access on gallery"
  ON gallery FOR SELECT
  TO anon, authenticated
  USING (true);

-- Create policies for authenticated users to manage gallery
CREATE POLICY "Allow authenticated users to manage gallery"
  ON gallery FOR ALL
  TO authenticated
  USING (true);

-- Insert sample data
INSERT INTO gallery (title, description, image_url, activity_type) VALUES 
('Gotong Royong Membersihkan Jalan', 'Kegiatan gotong royong warga dalam membersihkan jalan utama desa', 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=800', 'Gotong Royong'),
('Pelatihan UMKM', 'Pelatihan pengembangan usaha mikro kecil menengah untuk warga', 'https://images.unsplash.com/photo-1552664730-d307ca884978?w=800', 'Pelatihan'),
('Festival Budaya Desa', 'Perayaan festival budaya tahunan dengan berbagai pertunjukan tradisional', 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=800', 'Budaya')
ON CONFLICT DO NOTHING;
`;
}

// Run the test
if (require.main === module) {
  testGalleryFunctionality();
}

module.exports = { testGalleryFunctionality };
