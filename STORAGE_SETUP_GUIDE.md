# Storage Setup Guide for Gallery Feature

## Issue Identified
The image upload is failing with "new row violates row-level security policy" error. This means the storage bucket exists but RLS policies are blocking uploads.

## Manual Fix Required

Since the automated policy creation failed, you need to manually set up the RLS policies in your Supabase dashboard:

### Step 1: Go to Supabase Dashboard
1. Open your Supabase project dashboard
2. Navigate to **Storage** > **Policies**

### Step 2: Create Storage Policies
You need to create the following policies for the `storage.objects` table:

#### Policy 1: Allow Public Read Access
- **Policy Name**: `Allow public read access to images`
- **Table**: `storage.objects`
- **Operation**: `SELECT`
- **Target Roles**: `public`
- **Policy Definition**:
```sql
bucket_id = 'images'
```

#### Policy 2: Allow Authenticated Upload
- **Policy Name**: `Allow authenticated upload to images`
- **Table**: `storage.objects`
- **Operation**: `INSERT`
- **Target Roles**: `authenticated`
- **Policy Definition**:
```sql
bucket_id = 'images'
```

#### Policy 3: Allow Authenticated Delete
- **Policy Name**: `Allow authenticated delete from images`
- **Table**: `storage.objects`
- **Operation**: `DELETE`
- **Target Roles**: `authenticated`
- **Policy Definition**:
```sql
bucket_id = 'images'
```

#### Policy 4: Allow Authenticated Update
- **Policy Name**: `Allow authenticated update in images`
- **Table**: `storage.objects`
- **Operation**: `UPDATE`
- **Target Roles**: `authenticated`
- **Policy Definition**:
```sql
bucket_id = 'images'
```

### Step 3: Alternative SQL Method
If the UI method doesn't work, you can run these SQL commands in the SQL Editor:

```sql
-- Enable RLS on storage.objects (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Allow public read access to images bucket
CREATE POLICY "Allow public read access to images" 
ON storage.objects FOR SELECT 
TO public 
USING (bucket_id = 'images');

-- Allow authenticated users to upload to images bucket
CREATE POLICY "Allow authenticated upload to images" 
ON storage.objects FOR INSERT 
TO authenticated 
WITH CHECK (bucket_id = 'images');

-- Allow authenticated users to delete from images bucket
CREATE POLICY "Allow authenticated delete from images" 
ON storage.objects FOR DELETE 
TO authenticated 
USING (bucket_id = 'images');

-- Allow authenticated users to update in images bucket
CREATE POLICY "Allow authenticated update in images" 
ON storage.objects FOR UPDATE 
TO authenticated 
USING (bucket_id = 'images');
```

### Step 4: Verify Bucket Settings
Make sure your `images` bucket has these settings:
- **Public**: ✅ Enabled
- **File size limit**: 5MB (5242880 bytes)
- **Allowed MIME types**: 
  - `image/png`
  - `image/jpeg`
  - `image/jpg`
  - `image/gif`
  - `image/webp`

### Step 5: Test the Setup
After creating the policies, run this command to test:
```bash
node scripts/test-direct-upload.js
```

You should see:
```
✅ Direct upload successful!
✅ Public URL generated successfully
✅ File listing successful
✅ Test file cleaned up successfully
🎉 Direct upload test completed successfully!
```

## Common Issues and Solutions

### Issue: "Bucket not found"
- **Solution**: Make sure the bucket name is exactly `images` (lowercase)
- **Check**: Go to Storage > Buckets and verify the bucket exists

### Issue: "mime type not supported"
- **Solution**: Update bucket settings to allow image MIME types
- **Check**: Edit bucket settings and add the MIME types listed above

### Issue: "Insufficient permissions"
- **Solution**: Check that RLS policies are created correctly
- **Check**: Go to Storage > Policies and verify all 4 policies exist

### Issue: "new row violates row-level security policy"
- **Solution**: This is the main issue - RLS policies are missing or incorrect
- **Fix**: Follow the manual policy creation steps above

## Testing Your Gallery
Once the storage is properly configured:

1. Start your development server: `npm run dev`
2. Go to `http://localhost:3000/admin`
3. Navigate to the Gallery section
4. Try uploading an image
5. Check that it appears in the public gallery at `http://localhost:3000/galeri`

## Need Help?
If you're still having issues after following this guide:

1. Check the browser console for detailed error messages
2. Verify your environment variables are correct
3. Make sure you're logged in as an authenticated user when uploading
4. Check the Supabase logs in your dashboard for more details
