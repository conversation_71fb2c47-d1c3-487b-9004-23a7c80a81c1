import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Navbar } from "@/components/Navbar";
import { Footer } from "@/components/Footer";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Profile Pakan Rabaa Utara Duo",
  description:
    "Website Pakan Rabaa Utara Duo dengan informasi lengkap dan direktori UMKM",
  keywords: ["Pakan Rabaa Utara Duo", "Website Profile"],
  authors: [{ name: "Pakan Rabaa Utara Duo" }],
  creator: "Pakan <PERSON>baa Utara Duo",
  publisher: "Pakan Rabaa Utara Duo",
  robots: "index, follow",
  verification: {
    google: "24f9cc081f9ae37b",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://pakanrabaautaraduo.vercel.app",
    siteName: "Pakan Rabaa <PERSON>tara Duo",
    title: "Pakan Rabaa Utara Duo | Website Profile",
    description: "Website Profile Pakan Rabaa Utara Duo",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="id">
      <head>
        <meta
          name="google-site-verification"
          content="3P7bdoShFET1Oykdj7zv1CLwY0cXk_iH6acGgBy8FxE"
        />
      </head>
      <body className={inter.className}>
        <Navbar />
        <main className="min-h-screen">{children}</main>
        <Footer />
      </body>
    </html>
  );
}
