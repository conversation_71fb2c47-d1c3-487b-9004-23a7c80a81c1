const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Load environment variables manually
const envPath = path.join(__dirname, "..", ".env");
const envContent = fs.readFileSync(envPath, "utf8");
const envVars = {};

envContent.split("\n").forEach((line) => {
  const [key, value] = line.split("=");
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});

const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey =
  envVars.SUPABASE_SERVICE_ROLE_KEY || envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing Supabase environment variables");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testStorageFunctionality() {
  console.log("Testing storage functionality...");
  
  try {
    // Test 1: List storage buckets
    console.log("1. Testing storage bucket access...");
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();

    if (bucketsError) {
      console.error('❌ Error accessing storage buckets:', bucketsError.message);
    } else {
      console.log('✅ Successfully accessed storage buckets');
      console.log(`   Found ${buckets.length} buckets`);
      
      const imagesBucket = buckets.find(bucket => bucket.name === 'images');
      if (imagesBucket) {
        console.log('✅ Images bucket exists');
      } else {
        console.log('⚠️  Images bucket does not exist - will need to create it');
        await createImagesBucket();
      }
    }

    // Test 2: List files in images bucket
    console.log("2. Testing file listing in images bucket...");
    const { data: files, error: filesError } = await supabase.storage
      .from('images')
      .list('gallery', {
        limit: 10
      });

    if (filesError) {
      console.error('❌ Error listing files:', filesError.message);
      if (filesError.message.includes('Bucket not found')) {
        console.log('📝 Creating images bucket...');
        await createImagesBucket();
      }
    } else {
      console.log('✅ Successfully listed files in images/gallery folder');
      console.log(`   Found ${files.length} files`);
    }

    // Test 3: Test public URL generation
    console.log("3. Testing public URL generation...");
    const { data: publicUrlData } = supabase.storage
      .from('images')
      .getPublicUrl('gallery/test.jpg');

    if (publicUrlData && publicUrlData.publicUrl) {
      console.log('✅ Successfully generated public URL');
      console.log(`   URL format: ${publicUrlData.publicUrl}`);
    } else {
      console.log('❌ Failed to generate public URL');
    }

    console.log('\n🎉 Storage functionality test completed!');
    
  } catch (error) {
    console.error('❌ Unexpected error during storage testing:', error);
  }
}

async function createImagesBucket() {
  console.log('Creating images bucket...');
  
  try {
    const { data, error } = await supabase.storage.createBucket('images', {
      public: true,
      allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp'],
      fileSizeLimit: 5242880 // 5MB
    });

    if (error) {
      console.error('❌ Error creating images bucket:', error.message);
      console.log('\n📋 Manual Setup Required:');
      console.log('Please create an "images" bucket in your Supabase dashboard with:');
      console.log('- Public access enabled');
      console.log('- Allowed MIME types: image/png, image/jpeg, image/gif, image/webp');
      console.log('- File size limit: 5MB');
    } else {
      console.log('✅ Successfully created images bucket');
    }
    
  } catch (error) {
    console.error('❌ Error during bucket creation:', error);
  }
}

// Run the test
if (require.main === module) {
  testStorageFunctionality();
}

module.exports = { testStorageFunctionality };
